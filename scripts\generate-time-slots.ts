import { supabaseAdmin } from '../lib/supabase'

async function generateTimeSlots() {
  console.log('⏰ Generating time slots for the next 30 days...')

  try {
    // Get all active services
    const { data: services, error: servicesError } = await supabaseAdmin
      .from('services')
      .select('id, duration_minutes, buffer_time_minutes')
      .eq('is_active', true)

    if (servicesError) throw servicesError

    // Get employees for assignment
    const { data: employees, error: employeesError } = await supabaseAdmin
      .from('employees')
      .select('id')
      .eq('is_active', true)

    if (employeesError) throw employeesError

    const timeSlots = []
    const today = new Date()
    
    // Generate slots for the next 30 days
    for (let dayOffset = 0; dayOffset < 30; dayOffset++) {
      const currentDate = new Date(today)
      currentDate.setDate(today.getDate() + dayOffset)
      
      // Skip Sundays (day 0)
      if (currentDate.getDay() === 0) continue
      
      // Generate slots for each service
      for (const service of services!) {
        const slots = generateDailySlots(currentDate, service, employees!)
        timeSlots.push(...slots)
      }
    }

    // Clear existing future time slots
    const tomorrow = new Date()
    tomorrow.setDate(tomorrow.getDate() + 1)
    tomorrow.setHours(0, 0, 0, 0)

    await supabaseAdmin
      .from('time_slots')
      .delete()
      .gte('start_time', tomorrow.toISOString())

    // Insert new time slots in batches
    const batchSize = 100
    for (let i = 0; i < timeSlots.length; i += batchSize) {
      const batch = timeSlots.slice(i, i + batchSize)
      const { error: insertError } = await supabaseAdmin
        .from('time_slots')
        .insert(batch)

      if (insertError) {
        console.error(`Error inserting batch ${i / batchSize + 1}:`, insertError)
        throw insertError
      }
    }

    console.log(`✅ Generated ${timeSlots.length} time slots successfully!`)
    return { success: true, slotsGenerated: timeSlots.length }

  } catch (error) {
    console.error('❌ Error generating time slots:', error)
    throw error
  }
}

function generateDailySlots(date: Date, service: any, employees: any[]) {
  const slots = []
  
  // Business hours: 8:00 AM to 6:00 PM
  const startHour = 8
  const endHour = 18
  
  // Create time slots every 2 hours
  for (let hour = startHour; hour < endHour; hour += 2) {
    const startTime = new Date(date)
    startTime.setHours(hour, 0, 0, 0)
    
    const endTime = new Date(startTime)
    endTime.setMinutes(endTime.getMinutes() + service.duration_minutes)
    
    // Don't create slots that end after business hours
    if (endTime.getHours() > endHour) continue
    
    // Randomly assign an employee (for demo purposes)
    const randomEmployee = employees[Math.floor(Math.random() * employees.length)]
    
    slots.push({
      service_id: service.id,
      start_time: startTime.toISOString(),
      end_time: endTime.toISOString(),
      assigned_employee_id: randomEmployee.id,
      status: 'available' as const,
      notes: null
    })
  }
  
  return slots
}

// Run the generation if this file is executed directly
if (require.main === module) {
  generateTimeSlots()
    .then((result) => {
      console.log(`🎉 Time slot generation completed! Generated ${result.slotsGenerated} slots.`)
      process.exit(0)
    })
    .catch((error) => {
      console.error('💥 Time slot generation failed:', error)
      process.exit(1)
    })
}

export { generateTimeSlots }
