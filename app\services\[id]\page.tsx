"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ArrowLeft, Calendar, Clock, Loader2, MapPin, Shield, Users } from "lucide-react";
import Image from "next/image";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { PricingCalculator } from "../../../components/PricingCalculator";

interface PricingTier {
	id: string;
	tier_name: string;
	price: number;
	min_age: number;
	max_age: number | null;
}

interface Participant {
	id: string;
	age: number;
	tier: PricingTier;
	price: number;
}

interface Service {
	id: string;
	name: string;
	description: string | null;
	duration: string;
	capacity: string;
	ageLimit: string;
	category: string;
	location: string;
	features: string[];
	image_url: string | null;
	base_price: number;
	schedule: string[];
	gallery: string[];
	pricing_tiers: PricingTier[];
	max_participants: number;
}

export default function ServiceDetailPage() {
	const params = useParams();
	const router = useRouter();
	const serviceId = params.id as string;

	const [service, setService] = useState<Service | null>(null);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [totalPrice, setTotalPrice] = useState(0);
	const [participants, setParticipants] = useState<Participant[]>([]);
	const [selectedImageIndex, setSelectedImageIndex] = useState(0);

	useEffect(() => {
		const fetchService = async () => {
			try {
				setLoading(true);
				const response = await fetch(`/api/services/${serviceId}`);

				if (!response.ok) {
					throw new Error("Service not found");
				}

				const data = await response.json();
				setService(data.service);
			} catch (err) {
				setError(err instanceof Error ? err.message : "An error occurred");
			} finally {
				setLoading(false);
			}
		};

		if (serviceId) {
			fetchService();
		}
	}, [serviceId]);

	const handlePriceChange = (price: number, participantList: Participant[]) => {
		setTotalPrice(price);
		setParticipants(participantList);
	};

	const handleBookNow = () => {
		// Navigate to reservation page with pre-selected service
		router.push(`/reservation?service=${service?.id}`);
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 flex items-center justify-center">
				<div className="flex items-center gap-3">
					<Loader2 className="w-8 h-8 animate-spin text-emerald-600" />
					<span className="text-emerald-600 font-medium">Chargement du service...</span>
				</div>
			</div>
		);
	}

	if (error || !service) {
		return (
			<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 flex items-center justify-center">
				<div className="text-center">
					<div className="text-red-600 mb-4">
						<span className="text-lg font-semibold">Service non trouvé</span>
					</div>
					<p className="text-gray-600 mb-4">{error || "Le service demandé n'existe pas."}</p>
					<Button onClick={() => router.push("/services")} className="bg-emerald-600 hover:bg-emerald-700">
						Retour aux services
					</Button>
				</div>
			</div>
		);
	}

	const images =
		service.gallery.length > 0 ? service.gallery : [service.image_url || "/images/waterbikes_service.png"];

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			{/* Header */}
			<header className="bg-white/90 backdrop-blur-md shadow-sm sticky top-0 z-40">
				<div className="container mx-auto px-4 py-4">
					<div className="flex items-center gap-4">
						<Button
							variant="ghost"
							size="sm"
							onClick={() => router.push("/services")}
							className="flex items-center gap-2"
						>
							<ArrowLeft className="w-4 h-4" />
							Retour
						</Button>
						<div>
							<h1 className="text-xl font-bold text-gray-900">{service.name}</h1>
							<p className="text-sm text-emerald-600">{service.category}</p>
						</div>
					</div>
				</div>
			</header>

			<div className="container mx-auto px-4 py-8">
				<div className="grid lg:grid-cols-3 gap-8">
					{/* Left Column - Service Details */}
					<div className="lg:col-span-2 space-y-6">
						{/* Image Gallery */}
						<Card className="overflow-hidden">
							<div className="relative">
								<Image
									src={images[selectedImageIndex]}
									alt={service.name}
									width={800}
									height={400}
									className="w-full h-96 object-cover"
								/>
								<div className="absolute top-4 left-4">
									<Badge className="bg-white/90 text-emerald-700 font-semibold">
										{service.category}
									</Badge>
								</div>
							</div>

							{images.length > 1 && (
								<div className="p-4">
									<div className="flex gap-2 overflow-x-auto">
										{images.map((image, index) => (
											<button
												key={index}
												onClick={() => setSelectedImageIndex(index)}
												className={`flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
													selectedImageIndex === index
														? "border-emerald-500"
														: "border-gray-200 hover:border-gray-300"
												}`}
											>
												<Image
													src={image}
													alt={`${service.name} ${index + 1}`}
													width={80}
													height={80}
													className="w-full h-full object-cover"
												/>
											</button>
										))}
									</div>
								</div>
							)}
						</Card>

						{/* Service Information */}
						<Card>
							<CardHeader>
								<div className="flex items-start justify-between">
									<div>
										<CardTitle className="text-2xl text-gray-900 mb-2">{service.name}</CardTitle>
										<div className="flex items-center gap-4 text-sm text-gray-500">
											<div className="flex items-center">
												<MapPin className="w-4 h-4 mr-1" />
												{service.location}
											</div>
										</div>
									</div>
									<div className="text-right">
										<div className="text-lg font-bold text-emerald-600">{service.ageLimit}</div>
										<div className="text-sm text-gray-500">Conditions d'âge</div>
									</div>
								</div>
							</CardHeader>
							<CardContent className="space-y-6">
								<p className="text-gray-700 leading-relaxed">{service.description}</p>

								<div className="grid md:grid-cols-2 gap-4">
									<div className="flex items-center gap-2">
										<Clock className="w-5 h-5 text-emerald-600" />
										<div>
											<div className="font-medium">Durée</div>
											<div className="text-sm text-gray-600">{service.duration}</div>
										</div>
									</div>
									<div className="flex items-center gap-2">
										<Users className="w-5 h-5 text-emerald-600" />
										<div>
											<div className="font-medium">Capacité</div>
											<div className="text-sm text-gray-600">{service.capacity}</div>
										</div>
									</div>
								</div>

								<Separator />

								<div>
									<h3 className="font-semibold text-gray-900 mb-3">Inclus dans l'excursion</h3>
									<div className="grid md:grid-cols-2 gap-2">
										{service.features.map((feature, index) => (
											<div key={index} className="flex items-center gap-2">
												<Shield className="w-4 h-4 text-emerald-600" />
												<span className="text-sm">{feature}</span>
											</div>
										))}
									</div>
								</div>

								<Separator />

								<div>
									<h3 className="font-semibold text-gray-900 mb-3">Horaires disponibles</h3>
									<div className="flex flex-wrap gap-2">
										{service.schedule.map((time, index) => (
											<Badge key={index} variant="outline" className="px-3 py-1">
												<Calendar className="w-3 h-3 mr-1" />
												{time}
											</Badge>
										))}
									</div>
								</div>
							</CardContent>
						</Card>
					</div>

					{/* Right Column - Pricing Calculator */}
					<div className="space-y-6">
						<PricingCalculator
							pricingTiers={service.pricing_tiers}
							maxParticipants={service.max_participants}
							onPriceChange={handlePriceChange}
						/>

						{/* Booking Summary */}
						<Card>
							<CardHeader>
								<CardTitle>Réservation</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex justify-between items-center">
									<span className="text-gray-600">Participants</span>
									<span className="font-medium">{participants.length}</span>
								</div>
								<div className="flex justify-between items-center text-lg font-bold">
									<span>Total</span>
									<span className="text-emerald-600">{totalPrice}€</span>
								</div>
								<Button
									onClick={handleBookNow}
									disabled={participants.length === 0}
									className="w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white"
									size="lg"
								>
									Réserver Maintenant
								</Button>
								<p className="text-xs text-gray-500 text-center">
									Paiement sécurisé • Annulation gratuite 24h avant
								</p>
							</CardContent>
						</Card>
					</div>
				</div>
			</div>
		</div>
	);
}
