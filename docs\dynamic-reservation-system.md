# 🚀 Dynamic Reservation System Architecture

## 🎯 **System Overview**

The new dynamic reservation system eliminates the flawed pre-generated time slots approach and implements real-time availability calculation based on service rules and existing reservations.

## ❌ **Old System Problems (Fixed)**

1. **Pre-generated Time Slots**: Limited booking horizon, required manual generation
2. **One Service Per Slot**: Couldn't handle multiple services simultaneously  
3. **Shared Time Slots**: All services forced into same time intervals
4. **Inflexible Scheduling**: Fixed 2-hour blocks regardless of service duration

## ✅ **New Dynamic System**

### **Core Principle: Calculate Don't Store**
Instead of storing available time slots, we calculate them in real-time:

```
Customer Request: "WaterBike Tour, Feb 15, 3 people"
↓
1. Get service scheduling rules
2. Generate possible time slots for that date
3. Check existing reservations for conflicts
4. Check equipment/staff availability
5. Return available options
↓
Customer books → Create direct reservation
```

## 📊 **Database Architecture**

### **Removed Tables:**
- ❌ `time_slots` - No longer needed

### **Enhanced Tables:**

#### **`service_scheduling_rules`**
```sql
-- Flexible scheduling configuration per service
booking_interval_minutes INTEGER    -- e.g., 60 = hourly bookings
specific_times TIME[]              -- e.g., [09:00, 14:00] = fixed times
max_bookings_per_day INTEGER       -- Daily booking limit
operating_start_time TIME          -- Service operating hours
operating_end_time TIME
```

#### **`reservations`** (Enhanced)
```sql
-- Direct bookings without time slots
service_id UUID                    -- Which service
start_time TIMESTAMP              -- When it starts
end_time TIMESTAMP                -- When it ends
assigned_employee_id UUID         -- Who's assigned
booking_source VARCHAR            -- website/admin/phone
requires_confirmation BOOLEAN     -- Needs approval?
```

## 🔄 **Availability Calculation Algorithm**

### **Step 1: Generate Possible Times**
```typescript
// For WaterBike Tour (1h duration, hourly intervals, 8AM-6PM)
const possibleTimes = []
for (let hour = 8; hour <= 17; hour++) {
  possibleTimes.push({
    start: `${date} ${hour}:00`,
    end: `${date} ${hour + 1}:00`
  })
}
// Result: [8-9AM, 9-10AM, 10-11AM, ..., 5-6PM]
```

### **Step 2: Check Existing Reservations**
```sql
-- Find conflicting reservations
SELECT COUNT(*) FROM reservations 
WHERE service_id = 'waterbike-tour'
  AND start_time < '2024-02-15 10:00'
  AND end_time > '2024-02-15 09:00'
  AND status IN ('confirmed', 'pending')
```

### **Step 3: Check Equipment Availability**
```sql
-- Calculate equipment usage for time slot
SELECT SUM(r.participant_count * req.capacity_per_participant) as used_capacity
FROM reservations r
JOIN service_equipment_requirements req ON r.service_id = req.service_id
WHERE req.equipment_id = 'waterbikes'
  AND r.start_time < '2024-02-15 10:00'
  AND r.end_time > '2024-02-15 09:00'
  AND r.status IN ('confirmed', 'pending')
```

### **Step 4: Check Staff Availability**
```sql
-- Find available employees
SELECT e.id FROM employees e
LEFT JOIN reservations r ON e.id = r.assigned_employee_id
  AND r.start_time < '2024-02-15 10:00'
  AND r.end_time > '2024-02-15 09:00'
  AND r.status IN ('confirmed', 'pending')
WHERE r.id IS NULL  -- No conflicting bookings
  AND e.is_available_for_scheduling = true
```

## 🎨 **Service Scheduling Examples**

### **Hourly Service (WaterBike Tour)**
```json
{
  "service_id": "waterbike-tour",
  "duration_minutes": 60,
  "booking_interval_minutes": 60,
  "operating_start_time": "08:00",
  "operating_end_time": "18:00"
}
// Available: 8AM, 9AM, 10AM, 11AM, 12PM, 1PM, 2PM, 3PM, 4PM, 5PM
```

### **Fixed Times Service (Cultural Tour)**
```json
{
  "service_id": "cultural-tour", 
  "duration_minutes": 240,
  "specific_times": ["09:00", "14:00"],
  "max_bookings_per_day": 2
}
// Available: 9AM-1PM, 2PM-6PM only
```

### **Every 2 Hours (Mangrove Tour)**
```json
{
  "service_id": "mangrove-tour",
  "duration_minutes": 120, 
  "booking_interval_minutes": 120,
  "operating_start_time": "08:00",
  "operating_end_time": "16:00"
}
// Available: 8AM-10AM, 10AM-12PM, 12PM-2PM, 2PM-4PM
```

## 🚀 **API Implementation**

### **Availability Endpoint**
```typescript
GET /api/services/{serviceId}/availability?date=2024-02-15&participants=3

Response:
{
  "date": "2024-02-15",
  "service": {
    "id": "waterbike-tour",
    "name": "WaterBike Tour",
    "duration_minutes": 60
  },
  "available_times": [
    {
      "start_time": "2024-02-15T08:00:00Z",
      "end_time": "2024-02-15T09:00:00Z", 
      "available_capacity": 5,
      "assigned_employee": "Sophie Laroche"
    },
    {
      "start_time": "2024-02-15T09:00:00Z",
      "end_time": "2024-02-15T10:00:00Z",
      "available_capacity": 8,
      "assigned_employee": "Marc Dubois"
    }
  ]
}
```

### **Booking Endpoint**
```typescript
POST /api/reservations

{
  "service_id": "waterbike-tour",
  "start_time": "2024-02-15T09:00:00Z",
  "end_time": "2024-02-15T10:00:00Z",
  "participant_count": 3,
  "customer_id": "cust-123"
}
```

## 🎯 **Benefits**

✅ **Infinite Booking Horizon** - No pre-generation limits
✅ **Service-Specific Rules** - Each service has its own schedule  
✅ **Real-Time Accuracy** - Always current availability
✅ **Resource Efficiency** - No unused pre-generated slots
✅ **Flexible Scheduling** - Easy rule changes without regeneration
✅ **Multiple Services** - Different services can run simultaneously
✅ **Scalable** - Handles any number of services and rules

## 🔧 **Admin Interface Impact**

The admin interface will now focus on:
1. **Service Rule Configuration** - Set intervals, times, limits
2. **Reservation Management** - View/edit direct bookings
3. **Employee Assignment** - Manual override of auto-assignment
4. **Availability Monitoring** - Real-time capacity dashboard

This system is production-ready and can handle any service-based business model!
