"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Loader2, Plus, Minus, User, CreditCard, AlertCircle } from 'lucide-react'
import { ServiceWithPricing, TimeSlotWithAvailability, BookingFormData, ParticipantInfo, CustomerInfo } from '@/lib/types'
import ParticipantForm from './ParticipantForm'
import BookingSummary from './BookingSummary'
import { format } from 'date-fns'
import { fr } from 'date-fns/locale'

interface BookingFormProps {
  service: ServiceWithPricing
  timeSlot: TimeSlotWithAvailability
  date: Date
  onBookingComplete: () => void
}

export default function BookingForm({
  service,
  timeSlot,
  date,
  onBookingComplete
}: BookingFormProps) {
  const [participants, setParticipants] = useState<ParticipantInfo[]>([
    { firstName: '', lastName: '', age: 25 }
  ])
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    firstName: '',
    lastName: '',
    email: '',
    phone: ''
  })
  const [specialRequests, setSpecialRequests] = useState('')
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<string[]>([])
  const [warnings, setWarnings] = useState<string[]>([])

  const addParticipant = () => {
    if (participants.length < service.max_participants) {
      setParticipants([...participants, { firstName: '', lastName: '', age: 25 }])
    }
  }

  const removeParticipant = (index: number) => {
    if (participants.length > 1) {
      setParticipants(participants.filter((_, i) => i !== index))
    }
  }

  const updateParticipant = (index: number, participant: ParticipantInfo) => {
    const updated = [...participants]
    updated[index] = participant
    setParticipants(updated)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setErrors([])
    setWarnings([])

    try {
      const bookingData: BookingFormData = {
        serviceId: service.id,
        timeSlotId: timeSlot.id,
        participants,
        customerInfo,
        specialRequests: specialRequests || undefined
      }

      const response = await fetch('/api/bookings', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(bookingData)
      })

      const result = await response.json()

      if (!response.ok) {
        setErrors(result.details || [result.error])
        if (result.warnings) {
          setWarnings(result.warnings)
        }
        return
      }

      if (result.warnings) {
        setWarnings(result.warnings)
      }

      // Success
      onBookingComplete()

    } catch (error) {
      console.error('Error creating booking:', error)
      setErrors(['Une erreur est survenue lors de la création de la réservation'])
    } finally {
      setLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Booking Summary */}
      <BookingSummary
        service={service}
        timeSlot={timeSlot}
        date={date}
        participants={participants}
      />

      {/* Participants */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="w-5 h-5" />
            Participants ({participants.length})
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {participants.map((participant, index) => (
            <div key={index} className="relative">
              <ParticipantForm
                participant={participant}
                index={index}
                onChange={(updated) => updateParticipant(index, updated)}
                onRemove={participants.length > 1 ? () => removeParticipant(index) : undefined}
                pricingTiers={service.pricing_tiers}
              />
            </div>
          ))}

          {participants.length < service.max_participants && (
            <Button
              type="button"
              variant="outline"
              onClick={addParticipant}
              className="w-full"
            >
              <Plus className="w-4 h-4 mr-2" />
              Ajouter un participant
            </Button>
          )}
        </CardContent>
      </Card>

      {/* Customer Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="w-5 h-5" />
            Informations de contact
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="firstName">Prénom *</Label>
              <Input
                id="firstName"
                value={customerInfo.firstName}
                onChange={(e) => setCustomerInfo({...customerInfo, firstName: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="lastName">Nom *</Label>
              <Input
                id="lastName"
                value={customerInfo.lastName}
                onChange={(e) => setCustomerInfo({...customerInfo, lastName: e.target.value})}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={customerInfo.email}
                onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                required
              />
            </div>
            <div>
              <Label htmlFor="phone">Téléphone *</Label>
              <Input
                id="phone"
                type="tel"
                value={customerInfo.phone}
                onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="emergencyContactName">Contact d'urgence (nom)</Label>
              <Input
                id="emergencyContactName"
                value={customerInfo.emergencyContactName || ''}
                onChange={(e) => setCustomerInfo({...customerInfo, emergencyContactName: e.target.value})}
              />
            </div>
            <div>
              <Label htmlFor="emergencyContactPhone">Contact d'urgence (téléphone)</Label>
              <Input
                id="emergencyContactPhone"
                type="tel"
                value={customerInfo.emergencyContactPhone || ''}
                onChange={(e) => setCustomerInfo({...customerInfo, emergencyContactPhone: e.target.value})}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Special Requests */}
      <Card>
        <CardHeader>
          <CardTitle>Demandes spéciales</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            placeholder="Allergies, besoins spéciaux, demandes particulières..."
            value={specialRequests}
            onChange={(e) => setSpecialRequests(e.target.value)}
            rows={3}
          />
        </CardContent>
      </Card>

      {/* Errors and Warnings */}
      {errors.length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-5 h-5 text-red-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-red-800">Erreurs de validation</h4>
                <ul className="mt-1 text-sm text-red-700 list-disc list-inside">
                  {errors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {warnings.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-2">
              <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-yellow-800">Avertissements</h4>
                <ul className="mt-1 text-sm text-yellow-700 list-disc list-inside">
                  {warnings.map((warning, index) => (
                    <li key={index}>{warning}</li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Submit Button */}
      <Button
        type="submit"
        className="w-full"
        size="lg"
        disabled={loading}
      >
        {loading ? (
          <>
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
            Création de la réservation...
          </>
        ) : (
          'Confirmer la réservation'
        )}
      </Button>
    </form>
  )
}
