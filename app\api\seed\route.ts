import { NextRequest, NextResponse } from 'next/server'
import { seedDatabase } from '../../../scripts/seed-database'
import { generateTimeSlots } from '../../../scripts/generate-time-slots'

export async function POST(request: NextRequest) {
  try {
    // Check if we're in development mode
    if (process.env.NODE_ENV !== 'development') {
      return NextResponse.json(
        { error: 'Seeding is only allowed in development mode' },
        { status: 403 }
      )
    }

    console.log('🌱 Starting database seeding via API...')

    // Run the seeding
    await seedDatabase()
    
    // Generate time slots
    const timeSlotResult = await generateTimeSlots()

    return NextResponse.json({
      success: true,
      message: 'Database seeded successfully',
      details: {
        timeSlots: timeSlotResult.slotsGenerated
      }
    })

  } catch (error) {
    console.error('Error seeding database:', error)
    return NextResponse.json(
      { 
        error: 'Failed to seed database', 
        details: error instanceof Error ? error.message : 'Unknown error' 
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  return NextResponse.json({
    message: 'Use POST to seed the database',
    endpoints: {
      seed: 'POST /api/seed - Seeds the database with sample data'
    }
  })
}
