# 🏗️ Backend Architecture Guide - Dynamic Reservation System

## 🎯 **System Overview**

This document outlines the complete backend architecture for a production-ready, business-agnostic service scheduling and booking platform. The system uses **dynamic availability calculation** instead of pre-generated time slots, making it infinitely scalable and flexible.

## 📊 **Core Architecture Principles**

### **1. Dynamic Availability Calculation**

-   ❌ **No pre-generated time slots** - Calculate availability in real-time
-   ✅ **Service-specific rules** - Each service has its own scheduling patterns
-   ✅ **Real-time accuracy** - Always current, no stale data
-   ✅ **Infinite booking horizon** - No artificial limits

### **2. Direct Reservation System**

-   ❌ **No intermediate time slots** - Book directly to reservations table
-   ✅ **Service-specific scheduling** - 1h hourly vs 4h fixed times
-   ✅ **Resource management** - Equipment and staff availability
-   ✅ **Conflict prevention** - Real-time validation

## 🗄️ **Database Schema**

### **Core Tables**

#### **`services`**

```sql
-- Service definitions with scheduling metadata
id UUID PRIMARY KEY
name VARCHAR(255) -- "WaterBike Tour"
description TEXT
duration_minutes INTEGER -- 60, 120, 240
buffer_time_minutes INTEGER -- Cleanup time between bookings
base_price DECIMAL -- Base adult price
max_participants INTEGER -- Capacity limit
min_age INTEGER -- Age restrictions
is_family_friendly BOOLEAN
is_active BOOLEAN
default_employee_id UUID -- Preferred staff assignment
requires_qualification BOOLEAN -- Needs certified staff
auto_assign_employees BOOLEAN -- Auto vs manual assignment
```

#### **`service_scheduling_rules`**

```sql
-- Flexible scheduling configuration per service
id UUID PRIMARY KEY
service_id UUID REFERENCES services(id)
day_of_week INTEGER -- NULL = all days, 0-6 = specific day
min_advance_booking_hours INTEGER -- 24 = book 24h ahead
max_advance_booking_days INTEGER -- 365 = book up to 1 year
operating_start_time TIME -- 08:00
operating_end_time TIME -- 18:00

-- EITHER interval-based OR fixed-time scheduling:
booking_interval_minutes INTEGER -- 60 = hourly, 120 = every 2h
specific_times TIME[] -- [09:00, 14:00] = only 9AM and 2PM
max_bookings_per_day INTEGER -- Daily limit

CONSTRAINT scheduling_method_check -- Either intervals OR specific times
```

#### **`reservations`**

```sql
-- Direct bookings without intermediate time slots
id UUID PRIMARY KEY
customer_id UUID REFERENCES customers(id)
service_id UUID REFERENCES services(id) -- Direct service link
assigned_employee_id UUID REFERENCES employees(id)
start_time TIMESTAMP -- When service starts
end_time TIMESTAMP -- When service ends
participant_count INTEGER -- Total people
total_amount DECIMAL -- Final price
status VARCHAR -- pending, confirmed, cancelled, completed
booking_source VARCHAR -- website, admin, phone
requires_confirmation BOOLEAN -- Needs admin approval
confirmed_at TIMESTAMP
confirmed_by UUID
```

### **Resource Management Tables**

#### **`equipment`**

```sql
-- Equipment/resource definitions
id UUID PRIMARY KEY
name VARCHAR(255) -- "WaterBikes", "Kayaks"
description TEXT
total_capacity INTEGER -- 8 waterbikes available
capacity_per_participant INTEGER -- 1 bike per person
is_active BOOLEAN
```

#### **`service_equipment_requirements`**

```sql
-- What equipment each service needs
service_id UUID REFERENCES services(id)
equipment_id UUID REFERENCES equipment(id)
capacity_per_participant INTEGER -- How much equipment per person
```

#### **`equipment_reservations`**

```sql
-- Track equipment usage per reservation
reservation_id UUID REFERENCES reservations(id)
equipment_id UUID REFERENCES equipment(id)
quantity_reserved INTEGER -- How much equipment booked
```

### **Staff Management Tables**

#### **`employees`**

```sql
-- Staff with scheduling capabilities
id UUID REFERENCES profiles(id) -- Links to auth system
first_name, last_name, email, phone, role
skills TEXT[] -- ['navigation', 'kayak', 'photography']
languages TEXT[] -- ['français', 'anglais']
is_available_for_scheduling BOOLEAN
max_concurrent_services INTEGER -- Usually 1
scheduling_priority INTEGER -- 0-100, higher = preferred
default_hourly_rate DECIMAL
```

#### **`employee_availability`**

```sql
-- Weekly working hours per employee
employee_id UUID REFERENCES employees(id)
day_of_week INTEGER -- 0=Sunday, 1=Monday, etc.
start_time TIME -- 08:00
end_time TIME -- 17:00
is_available BOOLEAN
effective_from DATE -- When this schedule starts
effective_until DATE -- When it ends (NULL = indefinite)
```

#### **`employee_time_off`**

```sql
-- Vacation, sick days, time off
employee_id UUID REFERENCES employees(id)
start_date DATE
end_date DATE
start_time TIME -- For partial days
end_time TIME
type VARCHAR -- vacation, sick, training, personal
status VARCHAR -- pending, approved, denied
reason TEXT
```

#### **`employee_service_qualifications`**

```sql
-- Which employees can perform which services
employee_id UUID REFERENCES employees(id)
service_id UUID REFERENCES services(id)
qualification_level VARCHAR -- trainee, qualified, expert, instructor
certified_date DATE
expiry_date DATE
is_active BOOLEAN
```

### **Admin & Configuration Tables**

#### **`service_blackout_dates`**

```sql
-- Block service availability for holidays/maintenance
service_id UUID REFERENCES services(id)
start_date DATE
end_date DATE
reason TEXT -- "Christmas Holiday", "Equipment Maintenance"
is_active BOOLEAN
```

#### **`pricing_tiers`**

```sql
-- Age-based pricing (Child/Adult rates)
service_id UUID REFERENCES services(id)
tier_name VARCHAR -- "Enfant", "Adulte", "Senior"
min_age INTEGER -- 0, 13, 65
max_age INTEGER -- 12, NULL, NULL
price DECIMAL -- Actual price for this tier
is_active BOOLEAN
```

#### **`admin_audit_log`**

```sql
-- Track all administrative changes
admin_user_id UUID
action VARCHAR -- "CREATE_RESERVATION", "UPDATE_SERVICE"
table_name VARCHAR -- "reservations"
record_id UUID -- ID of affected record
old_values JSONB -- Before state
new_values JSONB -- After state
ip_address INET
user_agent TEXT
created_at TIMESTAMP
```

## 🔄 **Dynamic Availability Algorithm**

### **Core Availability Calculation Flow**

```typescript
async function calculateAvailability(serviceId: string, date: string, participantCount: number) {
	// 1. Get service and its scheduling rules
	const service = await getService(serviceId);
	const rules = await getServiceSchedulingRules(serviceId, date);

	// 2. Generate possible time slots based on rules
	const possibleSlots = generatePossibleTimeSlots(service, rules, date);

	// 3. Check each slot for availability
	const availableSlots = [];
	for (const slot of possibleSlots) {
		const isAvailable = await checkSlotAvailability(serviceId, slot.startTime, slot.endTime, participantCount);
		if (isAvailable) {
			availableSlots.push(slot);
		}
	}

	return availableSlots;
}
```

### **Time Slot Generation Logic**

```typescript
function generatePossibleTimeSlots(service, rules, date) {
	const slots = [];

	if (rules.booking_interval_minutes) {
		// INTERVAL-BASED: Generate slots every X minutes
		// Example: WaterBikes every 60 minutes from 8AM-6PM
		let currentTime = rules.operating_start_time;
		while (currentTime + service.duration_minutes <= rules.operating_end_time) {
			slots.push({
				startTime: `${date} ${currentTime}`,
				endTime: `${date} ${currentTime + service.duration_minutes}`,
			});
			currentTime += rules.booking_interval_minutes;
		}
	} else if (rules.specific_times) {
		// FIXED-TIME: Only specific start times allowed
		// Example: Cultural Tour only at 9AM and 2PM
		for (const startTime of rules.specific_times) {
			slots.push({
				startTime: `${date} ${startTime}`,
				endTime: `${date} ${startTime + service.duration_minutes}`,
			});
		}
	}

	return slots;
}
```

### **Availability Validation**

```typescript
async function checkSlotAvailability(serviceId, startTime, endTime, participantCount) {
	// 1. Check existing reservations for conflicts
	const conflictingReservations = await db.query(
		`
    SELECT COUNT(*) as conflicts
    FROM reservations 
    WHERE service_id = $1 
      AND start_time < $3 
      AND end_time > $2 
      AND status IN ('confirmed', 'pending')
  `,
		[serviceId, startTime, endTime]
	);

	if (conflictingReservations.conflicts > 0) return false;

	// 2. Check equipment availability
	const equipmentAvailable = await checkEquipmentAvailability(serviceId, startTime, endTime, participantCount);
	if (!equipmentAvailable) return false;

	// 3. Check staff availability
	const staffAvailable = await checkStaffAvailability(serviceId, startTime, endTime);
	if (!staffAvailable) return false;

	// 4. Check service-specific constraints
	const constraintsOk = await checkServiceConstraints(serviceId, startTime, participantCount);
	if (!constraintsOk) return false;

	return true;
}
```

### **Equipment Availability Check**

```typescript
async function checkEquipmentAvailability(serviceId, startTime, endTime, participantCount) {
	// Get equipment requirements for this service
	const requirements = await db.query(
		`
    SELECT e.id, e.total_capacity, req.capacity_per_participant
    FROM equipment e
    JOIN service_equipment_requirements req ON e.id = req.equipment_id
    WHERE req.service_id = $1
  `,
		[serviceId]
	);

	for (const equipment of requirements) {
		// Calculate how much equipment this booking would need
		const neededCapacity = participantCount * equipment.capacity_per_participant;

		// Check how much is already reserved during this time
		const reservedCapacity = await db.query(
			`
      SELECT COALESCE(SUM(er.quantity_reserved), 0) as reserved
      FROM equipment_reservations er
      JOIN reservations r ON er.reservation_id = r.id
      WHERE er.equipment_id = $1
        AND r.start_time < $3
        AND r.end_time > $2
        AND r.status IN ('confirmed', 'pending')
    `,
			[equipment.id, startTime, endTime]
		);

		const availableCapacity = equipment.total_capacity - reservedCapacity.reserved;

		if (availableCapacity < neededCapacity) {
			return false; // Not enough equipment available
		}
	}

	return true;
}
```

### **Staff Availability Check**

```typescript
async function checkStaffAvailability(serviceId, startTime, endTime) {
	// Find qualified employees for this service
	const qualifiedEmployees = await db.query(
		`
    SELECT DISTINCT e.id, e.scheduling_priority
    FROM employees e
    LEFT JOIN employee_service_qualifications esq ON e.id = esq.employee_id
    WHERE e.is_available_for_scheduling = true
      AND (
        NOT EXISTS (SELECT 1 FROM services WHERE id = $1 AND requires_qualification = true)
        OR (esq.service_id = $1 AND esq.is_active = true)
      )
    ORDER BY e.scheduling_priority DESC
  `,
		[serviceId]
	);

	for (const employee of qualifiedEmployees) {
		// Check if employee is working during this time
		const isWorking = await checkEmployeeWorkingHours(employee.id, startTime, endTime);
		if (!isWorking) continue;

		// Check if employee has time off
		const hasTimeOff = await checkEmployeeTimeOff(employee.id, startTime, endTime);
		if (hasTimeOff) continue;

		// Check if employee has conflicting reservations
		const hasConflict = await db.query(
			`
      SELECT COUNT(*) as conflicts
      FROM reservations
      WHERE assigned_employee_id = $1
        AND start_time < $3
        AND end_time > $2
        AND status IN ('confirmed', 'pending')
    `,
			[employee.id, startTime, endTime]
		);

		if (hasConflict.conflicts === 0) {
			return employee.id; // Found available employee
		}
	}

	return false; // No available staff
}
```

## 🚀 **API Endpoints**

### **Customer Booking APIs**

#### **GET /api/services**

```typescript
// List all active services with basic info
Response: {
	services: [
		{
			id: "uuid",
			name: "WaterBike Tour",
			description: "...",
			duration_minutes: 60,
			base_price: 25,
			max_participants: 8,
			min_age: 12,
			is_family_friendly: true,
			image_url: "/images/waterbikes.png",
		},
	];
}
```

#### **GET /api/services/[id]**

```typescript
// Get detailed service information
Response: {
  service: {
    id: "uuid",
    name: "WaterBike Tour",
    description: "...",
    duration_minutes: 60,
    buffer_time_minutes: 15,
    base_price: 25,
    max_participants: 8,
    min_age: 12,
    features: ["Equipment included", "Guide provided"],
    pricing_tiers: [
      { tier_name: "Child", min_age: 0, max_age: 12, price: 18 },
      { tier_name: "Adult", min_age: 13, max_age: null, price: 25 }
    ]
  }
}
```

#### **GET /api/services/[id]/availability**

```typescript
// Get available time slots for a service
Query: ?date=2024-02-15&participants=3

Response: {
  date: "2024-02-15",
  service_id: "uuid",
  available_times: [
    {
      start_time: "2024-02-15T08:00:00Z",
      end_time: "2024-02-15T09:00:00Z",
      available_capacity: 8,
      suggested_employee: {
        id: "uuid",
        name: "Sophie Laroche"
      }
    },
    {
      start_time: "2024-02-15T09:00:00Z",
      end_time: "2024-02-15T10:00:00Z",
      available_capacity: 5,
      suggested_employee: {
        id: "uuid",
        name: "Marc Dubois"
      }
    }
  ]
}
```

#### **POST /api/reservations**

```typescript
// Create a new reservation
Request: {
  service_id: "uuid",
  start_time: "2024-02-15T09:00:00Z",
  end_time: "2024-02-15T10:00:00Z",
  participant_count: 3,
  customer_info: {
    email: "<EMAIL>",
    first_name: "John",
    last_name: "Doe",
    phone: "+33 6 12 34 56 78"
  },
  participants_breakdown: {
    adults: 2,
    children: 1
  },
  special_requests: "Vegetarian lunch option"
}

Response: {
  reservation: {
    id: "uuid",
    reservation_number: "RES-2024-001",
    status: "pending",
    total_amount: 68, // 2 adults × 25€ + 1 child × 18€
    start_time: "2024-02-15T09:00:00Z",
    end_time: "2024-02-15T10:00:00Z",
    assigned_employee: {
      id: "uuid",
      name: "Sophie Laroche"
    }
  }
}
```

### **Admin Management APIs**

#### **Service Management**

**GET /api/admin/services**

```typescript
// List all services with admin details
Response: {
	services: [
		{
			id: "uuid",
			name: "WaterBike Tour",
			is_active: true,
			total_bookings: 156,
			revenue_this_month: 3900,
			next_booking: "2024-02-15T09:00:00Z",
			requires_qualification: false,
			default_employee: {
				id: "uuid",
				name: "Sophie Laroche",
			},
		},
	];
}
```

**PUT /api/admin/services/[id]**

```typescript
// Update service configuration
Request: {
  name: "Updated Service Name",
  duration_minutes: 90,
  base_price: 30,
  requires_qualification: true,
  is_active: false
}
```

**GET /api/admin/services/[id]/scheduling-rules**
**PUT /api/admin/services/[id]/scheduling-rules**

```typescript
// Manage service scheduling rules
Request: {
	rules: [
		{
			day_of_week: null, // All days
			operating_start_time: "08:00",
			operating_end_time: "18:00",
			booking_interval_minutes: 60,
			specific_times: null,
			max_bookings_per_day: null,
		},
	];
}
```

#### **Employee Management**

**GET /api/admin/employees**

```typescript
// List employees with availability status
Response: {
	employees: [
		{
			id: "uuid",
			name: "Sophie Laroche",
			role: "guide",
			is_available_for_scheduling: true,
			current_bookings: 3,
			next_booking: "2024-02-15T09:00:00Z",
			qualifications: ["WaterBike Tour", "Cultural Tour"],
			scheduling_priority: 80,
		},
	];
}
```

**GET /api/admin/employees/[id]/availability**
**PUT /api/admin/employees/[id]/availability**

```typescript
// Manage employee weekly availability
Request: {
	availability: [
		{
			day_of_week: 1, // Monday
			start_time: "08:00",
			end_time: "17:00",
			is_available: true,
		},
		{
			day_of_week: 2, // Tuesday
			start_time: "09:00",
			end_time: "16:00",
			is_available: true,
		},
	];
}
```

**POST /api/admin/employees/[id]/time-off**

```typescript
// Add employee time off
Request: {
  start_date: "2024-02-20",
  end_date: "2024-02-25",
  type: "vacation",
  reason: "Family vacation"
}
```

#### **Reservation Management**

**GET /api/admin/reservations**

```typescript
// List reservations with filters
Query: ?status=pending&date_from=2024-02-15&employee_id=uuid

Response: {
  reservations: [
    {
      id: "uuid",
      reservation_number: "RES-2024-001",
      customer: {
        name: "John Doe",
        email: "<EMAIL>",
        phone: "+33 6 12 34 56 78"
      },
      service: {
        name: "WaterBike Tour"
      },
      start_time: "2024-02-15T09:00:00Z",
      participant_count: 3,
      total_amount: 68,
      status: "pending",
      assigned_employee: {
        name: "Sophie Laroche"
      },
      booking_source: "website",
      created_at: "2024-02-14T15:30:00Z"
    }
  ],
  pagination: {
    total: 156,
    page: 1,
    per_page: 20
  }
}
```

**PUT /api/admin/reservations/[id]**

```typescript
// Update reservation (confirm, cancel, reassign)
Request: {
  status: "confirmed",
  assigned_employee_id: "uuid",
  admin_notes: "Confirmed by phone call"
}
```

**POST /api/admin/reservations/[id]/reassign**

```typescript
// Reassign reservation to different employee/time
Request: {
  new_employee_id: "uuid",
  new_start_time: "2024-02-15T10:00:00Z",
  reason: "Original employee unavailable"
}
```

#### **Schedule Management**

**GET /api/admin/schedule**

```typescript
// Get schedule overview for date range
Query: ?date_from=2024-02-15&date_to=2024-02-21&employee_id=uuid

Response: {
  schedule: [
    {
      date: "2024-02-15",
      employee: {
        id: "uuid",
        name: "Sophie Laroche"
      },
      bookings: [
        {
          reservation_id: "uuid",
          service_name: "WaterBike Tour",
          start_time: "09:00",
          end_time: "10:00",
          participant_count: 3,
          status: "confirmed"
        }
      ],
      availability: {
        working_hours: "08:00-17:00",
        available_slots: ["08:00-09:00", "10:00-11:00", "11:00-12:00"],
        unavailable_reason: null
      }
    }
  ]
}
```

**POST /api/admin/schedule/generate**

```typescript
// Bulk generate time slots (for template-based scheduling)
Request: {
  service_id: "uuid",
  date_from: "2024-02-15",
  date_to: "2024-02-29",
  template_id: "uuid", // Optional: use saved template
  override_existing: false
}
```

#### **Analytics & Reporting**

**GET /api/admin/analytics/overview**

```typescript
// Dashboard overview statistics
Response: {
  period: "2024-02",
  metrics: {
    total_reservations: 156,
    total_revenue: 4680,
    average_booking_value: 30,
    capacity_utilization: 0.78,
    popular_services: [
      { name: "WaterBike Tour", bookings: 45 },
      { name: "Cultural Tour", bookings: 32 }
    ],
    employee_utilization: [
      { name: "Sophie Laroche", utilization: 0.85 },
      { name: "Marc Dubois", utilization: 0.72 }
    ]
  }
}
```

**GET /api/admin/analytics/availability**

```typescript
// Availability analysis
Query: ?service_id=uuid&date_from=2024-02-01&date_to=2024-02-29

Response: {
  availability_stats: {
    total_possible_slots: 240,
    booked_slots: 187,
    utilization_rate: 0.78,
    peak_hours: ["10:00-12:00", "14:00-16:00"],
    low_demand_periods: ["08:00-09:00", "17:00-18:00"],
    bottlenecks: [
      {
        resource_type: "equipment",
        resource_name: "WaterBikes",
        constraint_hours: ["11:00-13:00"]
      }
    ]
  }
}
```

## 🔒 **Security & Authentication**

### **Role-Based Access Control**

```typescript
// User roles and permissions
enum UserRole {
	ADMIN = "admin", // Full system access
	EMPLOYEE = "employee", // Operational access
	CUSTOMER = "customer", // Booking access only
}

// Permission matrix
const permissions = {
	admin: [
		"read:all",
		"write:all",
		"delete:all",
		"manage:employees",
		"manage:services",
		"manage:reservations",
		"view:analytics",
		"manage:system",
	],
	employee: ["read:own_schedule", "read:reservations", "write:reservations", "read:customers", "read:services"],
	customer: ["read:services", "write:own_reservations", "read:own_reservations"],
};
```

### **API Authentication**

```typescript
// JWT-based authentication with Supabase
const authMiddleware = async (req, res, next) => {
	const token = req.headers.authorization?.replace("Bearer ", "");

	try {
		const { data: user, error } = await supabase.auth.getUser(token);
		if (error) throw error;

		req.user = user;
		next();
	} catch (error) {
		return res.status(401).json({ error: "Unauthorized" });
	}
};

// Role-based route protection
const requireRole = (roles: UserRole[]) => {
	return async (req, res, next) => {
		const userRole = req.user.app_metadata?.role;

		if (!roles.includes(userRole)) {
			return res.status(403).json({ error: "Insufficient permissions" });
		}

		next();
	};
};

// Usage
app.get("/api/admin/reservations", authMiddleware, requireRole([UserRole.ADMIN, UserRole.EMPLOYEE]), getReservations);
```

### **Row Level Security (RLS)**

```sql
-- Customers can only see their own reservations
CREATE POLICY "customers_own_reservations" ON reservations
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE id = reservations.customer_id
    )
  );

-- Employees can see reservations they're assigned to
CREATE POLICY "employees_assigned_reservations" ON reservations
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles
      WHERE role = 'employee'
      AND id = reservations.assigned_employee_id
    )
  );

-- Admins can see all reservations
CREATE POLICY "admins_all_reservations" ON reservations
  FOR ALL USING (
    auth.uid() IN (
      SELECT id FROM profiles WHERE role = 'admin'
    )
  );
```

## 🚀 **Performance Optimization**

### **Database Indexing Strategy**

```sql
-- Critical indexes for availability calculation
CREATE INDEX idx_reservations_service_time ON reservations(service_id, start_time, end_time);
CREATE INDEX idx_reservations_employee_time ON reservations(assigned_employee_id, start_time, end_time);
CREATE INDEX idx_reservations_status_time ON reservations(status, start_time)
  WHERE status IN ('confirmed', 'pending');

-- Equipment availability indexes
CREATE INDEX idx_equipment_reservations_time ON equipment_reservations(equipment_id, reservation_id);
CREATE INDEX idx_service_equipment_req ON service_equipment_requirements(service_id, equipment_id);

-- Employee scheduling indexes
CREATE INDEX idx_employee_availability_day ON employee_availability(employee_id, day_of_week);
CREATE INDEX idx_employee_time_off_dates ON employee_time_off(employee_id, start_date, end_date);
CREATE INDEX idx_employee_qualifications ON employee_service_qualifications(employee_id, service_id, is_active);
```

### **Caching Strategy**

```typescript
// Redis caching for frequently accessed data
const cacheKeys = {
	serviceRules: (serviceId: string) => `service:${serviceId}:rules`,
	employeeAvailability: (employeeId: string, date: string) => `employee:${employeeId}:availability:${date}`,
	equipmentCapacity: (equipmentId: string) => `equipment:${equipmentId}:capacity`,
};

// Cache service scheduling rules (rarely change)
const getServiceRules = async (serviceId: string) => {
	const cacheKey = cacheKeys.serviceRules(serviceId);
	let rules = await redis.get(cacheKey);

	if (!rules) {
		rules = await db.query("SELECT * FROM service_scheduling_rules WHERE service_id = $1", [serviceId]);
		await redis.setex(cacheKey, 3600, JSON.stringify(rules)); // Cache for 1 hour
	}

	return JSON.parse(rules);
};

// Cache availability calculations for popular time slots
const getAvailability = async (serviceId: string, date: string, participants: number) => {
	const cacheKey = `availability:${serviceId}:${date}:${participants}`;
	let availability = await redis.get(cacheKey);

	if (!availability) {
		availability = await calculateAvailability(serviceId, date, participants);
		await redis.setex(cacheKey, 300, JSON.stringify(availability)); // Cache for 5 minutes
	}

	return JSON.parse(availability);
};
```

### **Database Connection Pooling**

```typescript
// Optimized database connection management
const pool = new Pool({
	host: process.env.DB_HOST,
	port: 5432,
	database: process.env.DB_NAME,
	user: process.env.DB_USER,
	password: process.env.DB_PASSWORD,
	max: 20, // Maximum connections
	idleTimeoutMillis: 30000,
	connectionTimeoutMillis: 2000,
});

// Connection health monitoring
setInterval(async () => {
	try {
		await pool.query("SELECT 1");
	} catch (error) {
		console.error("Database health check failed:", error);
		// Implement alerting/recovery logic
	}
}, 30000);
```

## 📊 **Monitoring & Analytics**

### **Key Metrics to Track**

```typescript
// Business metrics
const businessMetrics = {
	// Revenue metrics
	totalRevenue: "SUM(total_amount) FROM reservations WHERE status = confirmed",
	averageBookingValue: "AVG(total_amount) FROM reservations WHERE status = confirmed",
	revenueByService: "GROUP BY service_id",

	// Operational metrics
	capacityUtilization: "booked_slots / total_available_slots",
	employeeUtilization: "working_hours / available_hours",
	equipmentUtilization: "reserved_capacity / total_capacity",

	// Customer metrics
	bookingConversionRate: "confirmed_bookings / total_inquiries",
	customerRetentionRate: "repeat_customers / total_customers",
	averageParticipantCount: "AVG(participant_count)",

	// Service quality metrics
	cancellationRate: "cancelled_bookings / total_bookings",
	noShowRate: "no_show_bookings / confirmed_bookings",
	advanceBookingTime: "AVG(start_time - created_at)",
};

// Performance metrics
const performanceMetrics = {
	apiResponseTime: "Average response time per endpoint",
	availabilityCalculationTime: "Time to calculate availability",
	databaseQueryTime: "Average query execution time",
	cacheHitRate: "Cache hits / total requests",
	errorRate: "Failed requests / total requests",
};
```

### **Real-time Monitoring**

```typescript
// Monitoring middleware
const monitoringMiddleware = (req, res, next) => {
	const startTime = Date.now();

	res.on("finish", () => {
		const duration = Date.now() - startTime;
		const metric = {
			endpoint: req.path,
			method: req.method,
			statusCode: res.statusCode,
			duration,
			timestamp: new Date().toISOString(),
			userId: req.user?.id,
		};

		// Send to monitoring service (e.g., DataDog, New Relic)
		monitoring.track("api_request", metric);

		// Alert on slow requests
		if (duration > 5000) {
			alerts.send("slow_request", metric);
		}
	});

	next();
};
```

## 🔄 **Error Handling & Recovery**

### **Graceful Error Handling**

```typescript
// Centralized error handling
class AppError extends Error {
	constructor(public message: string, public statusCode: number, public isOperational: boolean = true) {
		super(message);
		this.name = this.constructor.name;
		Error.captureStackTrace(this, this.constructor);
	}
}

// Error types
class ValidationError extends AppError {
	constructor(message: string) {
		super(message, 400);
	}
}

class ConflictError extends AppError {
	constructor(message: string) {
		super(message, 409);
	}
}

class NotFoundError extends AppError {
	constructor(message: string) {
		super(message, 404);
	}
}

// Global error handler
const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
	if (error instanceof AppError) {
		return res.status(error.statusCode).json({
			error: error.message,
			code: error.constructor.name,
		});
	}

	// Log unexpected errors
	console.error("Unexpected error:", error);

	return res.status(500).json({
		error: "Internal server error",
		code: "INTERNAL_ERROR",
	});
};
```

### **Booking Conflict Resolution**

```typescript
// Handle concurrent booking attempts
const createReservation = async (bookingData) => {
	const transaction = await db.beginTransaction();

	try {
		// Lock the time slot to prevent concurrent bookings
		await transaction.query(
			`
      SELECT 1 FROM reservations 
      WHERE service_id = $1 
        AND start_time < $3 
        AND end_time > $2 
        AND status IN ('confirmed', 'pending')
      FOR UPDATE
    `,
			[bookingData.serviceId, bookingData.startTime, bookingData.endTime]
		);

		// Re-check availability within transaction
		const isAvailable = await checkSlotAvailability(
			bookingData.serviceId,
			bookingData.startTime,
			bookingData.endTime,
			bookingData.participantCount,
			transaction
		);

		if (!isAvailable) {
			throw new ConflictError("Time slot no longer available");
		}

		// Create reservation
		const reservation = await transaction.query(
			`
      INSERT INTO reservations (customer_id, service_id, start_time, end_time, participant_count, total_amount, status)
      VALUES ($1, $2, $3, $4, $5, $6, 'pending')
      RETURNING *
    `,
			[
				bookingData.customerId,
				bookingData.serviceId,
				bookingData.startTime,
				bookingData.endTime,
				bookingData.participantCount,
				bookingData.totalAmount,
			]
		);

		// Reserve equipment
		await reserveEquipment(reservation.id, bookingData.serviceId, bookingData.participantCount, transaction);

		await transaction.commit();
		return reservation;
	} catch (error) {
		await transaction.rollback();
		throw error;
	}
};
```

## 🎯 **Implementation Roadmap**

### **Phase 1: Core Booking System (Weeks 1-2)**

1. ✅ Database schema (completed)
2. ✅ Basic service and equipment APIs
3. ✅ Dynamic availability calculation
4. ✅ Reservation creation and management

### **Phase 2: Admin Interface (Weeks 3-4)**

1. Employee management APIs
2. Service configuration APIs
3. Schedule management dashboard
4. Reservation management interface

### **Phase 3: Advanced Features (Weeks 5-6)**

1. Employee availability management
2. Time off and blackout date handling
3. Qualification-based assignment
4. Bulk operations and templates

### **Phase 4: Analytics & Optimization (Weeks 7-8)**

1. Performance monitoring
2. Business analytics dashboard
3. Capacity optimization tools
4. Automated reporting

### **Phase 5: Production Readiness (Weeks 9-10)**

1. Load testing and optimization
2. Security audit and hardening
3. Backup and disaster recovery
4. Documentation and training

## 🔧 **Implementation Examples**

### **Service Configuration Examples**

#### **Hourly Service (WaterBike Tour)**

```typescript
// Service configuration
const waterbikeService = {
	name: "Excursion en WaterBikes",
	duration_minutes: 60,
	buffer_time_minutes: 15,
	base_price: 25,
	max_participants: 8,
	requires_qualification: false,
};

// Scheduling rules
const waterbikeRules = {
	booking_interval_minutes: 60, // Every hour
	operating_start_time: "08:00",
	operating_end_time: "17:00", // Last booking at 5PM (ends at 6PM)
	min_advance_booking_hours: 24,
	max_advance_booking_days: 365,
};

// Equipment requirements
const waterbikeEquipment = [{ equipment_name: "WaterBikes", capacity_per_participant: 1 }];

// Generated time slots for Feb 15:
// 08:00-09:00, 09:00-10:00, 10:00-11:00, ..., 17:00-18:00
```

#### **Fixed-Time Service (Cultural Tour)**

```typescript
// Service configuration
const culturalService = {
	name: "Visite Guidée Culturelle",
	duration_minutes: 240, // 4 hours
	buffer_time_minutes: 30,
	base_price: 30,
	max_participants: 12,
	requires_qualification: false,
};

// Scheduling rules
const culturalRules = {
	specific_times: ["09:00", "14:00"], // Only 9AM and 2PM
	max_bookings_per_day: 2,
	operating_start_time: "08:00",
	operating_end_time: "18:00",
	min_advance_booking_hours: 48, // Requires 2 days advance notice
};

// Generated time slots for Feb 15:
// 09:00-13:00, 14:00-18:00 (only these two options)
```

### **Booking Flow Implementation**

#### **Customer Booking Process**

```typescript
// 1. Customer selects service and date
const availabilityRequest = {
	serviceId: "waterbike-tour-uuid",
	date: "2024-02-15",
	participants: 3,
};

// 2. System calculates availability
const availability = await calculateAvailability(availabilityRequest);
// Returns: [
//   { start_time: "08:00", end_time: "09:00", available: true },
//   { start_time: "09:00", end_time: "10:00", available: false }, // Booked
//   { start_time: "10:00", end_time: "11:00", available: true }
// ]

// 3. Customer selects time slot
const bookingRequest = {
	serviceId: "waterbike-tour-uuid",
	startTime: "2024-02-15T10:00:00Z",
	endTime: "2024-02-15T11:00:00Z",
	participantCount: 3,
	participantBreakdown: { adults: 2, children: 1 },
	customerInfo: {
		email: "<EMAIL>",
		firstName: "John",
		lastName: "Doe",
		phone: "+33 6 12 34 56 78",
	},
};

// 4. System validates and creates reservation
const reservation = await createReservation(bookingRequest);
// Returns: {
//   id: "reservation-uuid",
//   reservationNumber: "RES-2024-001",
//   status: "pending",
//   totalAmount: 68, // 2×25€ + 1×18€
//   assignedEmployee: { name: "Sophie Laroche" }
// }
```

#### **Admin Confirmation Process**

```typescript
// Admin reviews pending reservation
const pendingReservations = await getPendingReservations();

// Admin confirms reservation
await confirmReservation("reservation-uuid", {
	confirmedBy: "admin-uuid",
	assignedEmployeeId: "sophie-uuid", // Can reassign if needed
	adminNotes: "Confirmed - customer called to verify details",
});

// System automatically:
// 1. Updates reservation status to "confirmed"
// 2. Reserves equipment
// 3. Blocks employee schedule
// 4. Sends confirmation email to customer
```

### **Equipment Management Implementation**

#### **Equipment Availability Calculation**

```typescript
async function checkEquipmentAvailability(serviceId, startTime, endTime, participantCount) {
	// Get equipment requirements for the service
	const requirements = await db.query(
		`
    SELECT
      e.id,
      e.name,
      e.total_capacity,
      req.capacity_per_participant
    FROM equipment e
    JOIN service_equipment_requirements req ON e.id = req.equipment_id
    WHERE req.service_id = $1
  `,
		[serviceId]
	);

	for (const equipment of requirements) {
		// Calculate needed capacity
		const neededCapacity = participantCount * equipment.capacity_per_participant;

		// Get currently reserved capacity during this time
		const { rows } = await db.query(
			`
      SELECT COALESCE(SUM(er.quantity_reserved), 0) as reserved_capacity
      FROM equipment_reservations er
      JOIN reservations r ON er.reservation_id = r.id
      WHERE er.equipment_id = $1
        AND r.start_time < $3
        AND r.end_time > $2
        AND r.status IN ('confirmed', 'pending')
    `,
			[equipment.id, startTime, endTime]
		);

		const reservedCapacity = rows[0].reserved_capacity;
		const availableCapacity = equipment.total_capacity - reservedCapacity;

		if (availableCapacity < neededCapacity) {
			return {
				available: false,
				reason: `Insufficient ${equipment.name}: need ${neededCapacity}, only ${availableCapacity} available`,
				equipment: equipment.name,
			};
		}
	}

	return { available: true };
}

// Example usage:
// WaterBike Tour needs 3 participants × 1 bike each = 3 bikes
// Total WaterBikes: 8
// Currently reserved during 10-11AM: 2 bikes
// Available: 8 - 2 = 6 bikes
// Result: ✅ Available (6 >= 3)
```

#### **Equipment Reservation Process**

```typescript
async function reserveEquipment(reservationId, serviceId, participantCount, transaction) {
	// Get equipment requirements
	const requirements = await transaction.query(
		`
    SELECT equipment_id, capacity_per_participant
    FROM service_equipment_requirements
    WHERE service_id = $1
  `,
		[serviceId]
	);

	// Create equipment reservations
	for (const req of requirements) {
		const quantityNeeded = participantCount * req.capacity_per_participant;

		await transaction.query(
			`
      INSERT INTO equipment_reservations (reservation_id, equipment_id, quantity_reserved)
      VALUES ($1, $2, $3)
    `,
			[reservationId, req.equipment_id, quantityNeeded]
		);
	}
}
```

### **Employee Assignment Implementation**

#### **Intelligent Employee Assignment**

```typescript
async function assignEmployee(serviceId, startTime, endTime) {
	// Find qualified employees
	const qualifiedEmployees = await db.query(
		`
    SELECT
      e.id,
      e.first_name,
      e.last_name,
      e.scheduling_priority,
      esq.qualification_level
    FROM employees e
    LEFT JOIN employee_service_qualifications esq ON e.id = esq.employee_id AND esq.service_id = $1
    WHERE e.is_available_for_scheduling = true
      AND (
        -- Service doesn't require qualification
        NOT EXISTS (SELECT 1 FROM services WHERE id = $1 AND requires_qualification = true)
        OR
        -- Employee has qualification for this service
        (esq.is_active = true AND esq.qualification_level IN ('qualified', 'expert', 'instructor'))
      )
    ORDER BY
      e.scheduling_priority DESC,
      esq.qualification_level DESC,
      e.first_name
  `,
		[serviceId]
	);

	// Check availability for each qualified employee
	for (const employee of qualifiedEmployees) {
		const isAvailable = await checkEmployeeAvailability(employee.id, startTime, endTime);

		if (isAvailable) {
			return {
				employeeId: employee.id,
				name: `${employee.first_name} ${employee.last_name}`,
				qualificationLevel: employee.qualification_level || "qualified",
			};
		}
	}

	return null; // No available employee
}

async function checkEmployeeAvailability(employeeId, startTime, endTime) {
	const startDate = new Date(startTime);
	const dayOfWeek = startDate.getDay();
	const timeOnly = startTime.split("T")[1].substring(0, 5); // "10:00"

	// Check working hours
	const workingHours = await db.query(
		`
    SELECT start_time, end_time
    FROM employee_availability
    WHERE employee_id = $1
      AND day_of_week = $2
      AND is_available = true
      AND (effective_from IS NULL OR effective_from <= $3)
      AND (effective_until IS NULL OR effective_until >= $3)
  `,
		[employeeId, dayOfWeek, startDate.toISOString().split("T")[0]]
	);

	if (workingHours.length === 0) return false;

	// Check if booking time falls within working hours
	const workStart = workingHours[0].start_time;
	const workEnd = workingHours[0].end_time;

	if (timeOnly < workStart || timeOnly >= workEnd) return false;

	// Check for time off
	const timeOff = await db.query(
		`
    SELECT 1
    FROM employee_time_off
    WHERE employee_id = $1
      AND start_date <= $2
      AND end_date >= $2
      AND status = 'approved'
  `,
		[employeeId, startDate.toISOString().split("T")[0]]
	);

	if (timeOff.length > 0) return false;

	// Check for conflicting reservations
	const conflicts = await db.query(
		`
    SELECT 1
    FROM reservations
    WHERE assigned_employee_id = $1
      AND start_time < $3
      AND end_time > $2
      AND status IN ('confirmed', 'pending')
  `,
		[employeeId, startTime, endTime]
	);

	return conflicts.length === 0;
}
```

### **Pricing Calculation Implementation**

#### **Dynamic Pricing Calculation**

```typescript
async function calculateTotalPrice(serviceId, participantBreakdown) {
	// Get pricing tiers for the service
	const pricingTiers = await db.query(
		`
    SELECT tier_name, min_age, max_age, price
    FROM pricing_tiers
    WHERE service_id = $1 AND is_active = true
    ORDER BY min_age
  `,
		[serviceId]
	);

	let totalAmount = 0;

	// Calculate price for each participant category
	for (const [category, count] of Object.entries(participantBreakdown)) {
		let tierPrice = 0;

		// Map category to age range and find appropriate tier
		switch (category) {
			case "children":
				tierPrice = pricingTiers.find((t) => t.tier_name === "Enfant")?.price || 0;
				break;
			case "adults":
				tierPrice = pricingTiers.find((t) => t.tier_name === "Adulte")?.price || 0;
				break;
			case "seniors":
				tierPrice = pricingTiers.find((t) => t.tier_name === "Senior")?.price || 0;
				break;
		}

		totalAmount += tierPrice * count;
	}

	return {
		subtotal: totalAmount,
		breakdown: participantBreakdown,
		pricingTiers: pricingTiers,
	};
}

// Example:
// Service: WaterBike Tour
// Participants: { adults: 2, children: 1 }
// Pricing: Adult = 25€, Child = 18€
// Total: (2 × 25€) + (1 × 18€) = 68€
```

## 🔄 **Business Logic Workflows**

### **Complete Booking Workflow**

```mermaid
graph TD
    A[Customer selects service & date] --> B[System calculates availability]
    B --> C{Available slots?}
    C -->|No| D[Show alternative dates/times]
    C -->|Yes| E[Customer selects time slot]
    E --> F[Customer enters details]
    F --> G[System validates booking]
    G --> H{Validation passed?}
    H -->|No| I[Show error message]
    H -->|Yes| J[Create pending reservation]
    J --> K[Reserve equipment]
    K --> L[Assign employee]
    L --> M[Calculate final price]
    M --> N[Send confirmation email]
    N --> O{Requires admin confirmation?}
    O -->|No| P[Auto-confirm reservation]
    O -->|Yes| Q[Admin reviews booking]
    Q --> R{Admin approves?}
    R -->|No| S[Cancel reservation]
    R -->|Yes| T[Confirm reservation]
    P --> U[Booking complete]
    T --> U
```

### **Admin Schedule Management Workflow**

```mermaid
graph TD
    A[Admin opens schedule view] --> B[Select date range]
    B --> C[Load employee schedules]
    C --> D[Display calendar with bookings]
    D --> E{Admin action?}
    E -->|View details| F[Show booking details]
    E -->|Reassign| G[Select new employee/time]
    E -->|Cancel| H[Cancel booking]
    E -->|Add time off| I[Block employee availability]
    E -->|Bulk operations| J[Generate/modify multiple slots]
    F --> D
    G --> K[Validate new assignment]
    K --> L{Valid?}
    L -->|No| M[Show conflict error]
    L -->|Yes| N[Update reservation]
    N --> D
```

This architecture provides a solid foundation for a scalable, production-ready service booking platform that can adapt to any service-based business model while maintaining high performance and reliability.
