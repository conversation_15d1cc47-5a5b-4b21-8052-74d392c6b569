"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Loader2, Database, CheckCircle, AlertCircle } from 'lucide-react'

export default function SeedPage() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  const handleSeed = async () => {
    setLoading(true)
    setError(null)
    setResult(null)

    try {
      const response = await fetch('/api/seed', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Failed to seed database')
      }

      setResult(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 p-8">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-6 h-6" />
              Database Seeding
            </CardTitle>
            <p className="text-muted-foreground">
              Populate the database with sample data for testing the website.
            </p>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2">⚠️ Warning</h4>
              <p className="text-sm text-yellow-700">
                This will clear existing data and populate the database with sample data. 
                Only use this in development environment.
              </p>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium">What will be created:</h4>
              <ul className="text-sm text-muted-foreground space-y-1 ml-4">
                <li>• 4 Equipment types (WaterBikes, Kayaks, Boats, Observation gear)</li>
                <li>• 6 Services (WaterBikes tour, Cultural tour, Pelican encounter, etc.)</li>
                <li>• Pricing tiers (Adult/Child rates for each service)</li>
                <li>• 2 Sample employees (Sophie, Marc)</li>
                <li>• 2 Sample customers (Marie, Jean)</li>
                <li>• Time slots for the next 30 days</li>
              </ul>
            </div>

            <Button 
              onClick={handleSeed} 
              disabled={loading}
              className="w-full"
              size="lg"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Seeding Database...
                </>
              ) : (
                <>
                  <Database className="w-4 h-4 mr-2" />
                  Seed Database
                </>
              )}
            </Button>

            {result && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <CheckCircle className="w-5 h-5 text-green-600" />
                  <h4 className="font-medium text-green-800">Success!</h4>
                </div>
                <p className="text-sm text-green-700 mb-2">{result.message}</p>
                {result.details && (
                  <div className="text-xs text-green-600">
                    <p>Time slots generated: {result.details.timeSlots}</p>
                  </div>
                )}
              </div>
            )}

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center gap-2 mb-2">
                  <AlertCircle className="w-5 h-5 text-red-600" />
                  <h4 className="font-medium text-red-800">Error</h4>
                </div>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}

            <div className="text-center">
              <p className="text-sm text-muted-foreground">
                After seeding, you can test the booking flow at{' '}
                <a href="/reservation" className="text-emerald-600 hover:underline">
                  /reservation
                </a>
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
