import { NextRequest, NextResponse } from 'next/server'
import { checkAvailability, getAvailableTimeSlots } from '@/lib/availability'
import { AvailabilityRequest } from '@/lib/types'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { searchParams } = new URL(request.url)
    const date = searchParams.get('date')
    const participantCount = parseInt(searchParams.get('participants') || '1')

    if (!date) {
      return NextResponse.json(
        { error: 'Date parameter is required' },
        { status: 400 }
      )
    }

    // Validate date format
    const dateObj = new Date(date)
    if (isNaN(dateObj.getTime())) {
      return NextResponse.json(
        { error: 'Invalid date format. Use YYYY-MM-DD' },
        { status: 400 }
      )
    }

    // Check if date is in the past
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    if (dateObj < today) {
      return NextResponse.json(
        { error: 'Cannot check availability for past dates' },
        { status: 400 }
      )
    }

    // Validate participant count
    if (participantCount < 1 || participantCount > 50) {
      return NextResponse.json(
        { error: 'Participant count must be between 1 and 50' },
        { status: 400 }
      )
    }

    const serviceId = params.id

    // Check availability for the requested date
    const availabilityRequest: AvailabilityRequest = {
      serviceId,
      date,
      participantCount
    }

    const availability = await checkAvailability(availabilityRequest)

    return NextResponse.json({
      success: true,
      data: availability
    })

  } catch (error) {
    console.error('Error checking availability:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json()
    const { dates, participants = 1 } = body

    if (!dates || !Array.isArray(dates)) {
      return NextResponse.json(
        { error: 'Dates array is required' },
        { status: 400 }
      )
    }

    if (dates.length > 31) {
      return NextResponse.json(
        { error: 'Cannot check more than 31 dates at once' },
        { status: 400 }
      )
    }

    const serviceId = params.id
    const results = []

    // Check availability for each date
    for (const date of dates) {
      try {
        const availabilityRequest: AvailabilityRequest = {
          serviceId,
          date,
          participantCount: participants
        }

        const availability = await checkAvailability(availabilityRequest)
        results.push(availability)
      } catch (error) {
        console.error(`Error checking availability for date ${date}:`, error)
        results.push({
          date,
          timeSlots: [],
          error: 'Failed to check availability'
        })
      }
    }

    return NextResponse.json({
      success: true,
      data: results
    })

  } catch (error) {
    console.error('Error in bulk availability check:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
