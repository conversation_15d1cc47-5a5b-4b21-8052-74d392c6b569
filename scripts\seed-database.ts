import { supabaseAdmin } from "../lib/supabase";

async function seedDatabase() {
	console.log("🌱 Starting database seeding...");

	try {
		// 1. Clear existing data (in reverse dependency order)
		console.log("🧹 Clearing existing data...");
		await supabaseAdmin.from("equipment_reservations").delete().neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin.from("reservation_participants").delete().neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin.from("reservations").delete().neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin.from("time_slots").delete().neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin.from("pricing_tiers").delete().neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin
			.from("service_equipment_requirements")
			.delete()
			.neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin.from("equipment").delete().neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin.from("services").delete().neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin.from("employees").delete().neq("id", "00000000-0000-0000-0000-000000000000");
		await supabaseAdmin.from("customers").delete().neq("id", "00000000-0000-0000-0000-000000000000");

		// 2. Insert Equipment
		console.log("🛠️ Inserting equipment...");
		const { data: equipment, error: equipmentError } = await supabaseAdmin
			.from("equipment")
			.insert([
				{
					id: "eq-waterbikes",
					name: "WaterBikes",
					description: "Vélos aquatiques écologiques",
					total_capacity: 8,
					status: "operational",
				},
				{
					id: "eq-kayaks",
					name: "Kayaks",
					description: "Kayaks pour exploration mangrove",
					total_capacity: 6,
					status: "operational",
				},
				{
					id: "eq-boat-small",
					name: "Petit Bateau",
					description: "Bateau pour visites guidées",
					total_capacity: 12,
					status: "operational",
				},
				{
					id: "eq-observation-gear",
					name: "Matériel d'observation",
					description: "Jumelles et équipement d'observation",
					total_capacity: 15,
					status: "operational",
				},
			])
			.select();

		if (equipmentError) throw equipmentError;

		// 3. Insert Services
		console.log("🏖️ Inserting services...");
		const { data: services, error: servicesError } = await supabaseAdmin
			.from("services")
			.insert([
				{
					id: "srv-waterbikes",
					name: "Excursion en WaterBikes",
					description:
						"Explorez le littoral de manière ludique et éco-responsable avec nos vélos aquatiques. Une aventure unique pour découvrir la beauté des côtes guadeloupéennes.",
					duration_minutes: 120,
					buffer_time_minutes: 30,
					base_price: 25,
					max_participants: 8,
					min_age: 12,
					max_age: null,
					is_family_friendly: true,
					is_active: true,
					image_url: "/images/waterbikes_service.png",
				},
				{
					id: "srv-cultural-tour",
					name: "Visite Guidée Culturelle",
					description:
						"Plongez au cœur de l'histoire guadeloupéenne avec notre guide passionné. Découvrez les traditions, l'architecture et les légendes locales.",
					duration_minutes: 180,
					buffer_time_minutes: 15,
					base_price: 30,
					max_participants: 12,
					min_age: 0,
					max_age: null,
					is_family_friendly: true,
					is_active: true,
					image_url: "/images/cultural_tour_service.png",
				},
				{
					id: "srv-pelican-encounter",
					name: "Rencontre avec les Pélicans",
					description:
						"Aventure unique au cœur d'un îlet sauvage pour observer les pélicans dans leur habitat naturel. Une expérience inoubliable pour toute la famille.",
					duration_minutes: 180,
					buffer_time_minutes: 30,
					base_price: 45,
					max_participants: 8,
					min_age: 0,
					max_age: null,
					is_family_friendly: true,
					is_active: true,
					image_url: "/images/pelican_encounter_service.png",
				},
				{
					id: "srv-local-products",
					name: "Dégustation de Produits Locaux",
					description:
						"Expérience gustative avec des saveurs authentiques de la Guadeloupe. Découvrez les produits du terroir avec notre guide gastronome.",
					duration_minutes: 90,
					buffer_time_minutes: 15,
					base_price: 35,
					max_participants: 15,
					min_age: 0,
					max_age: null,
					is_family_friendly: true,
					is_active: true,
					image_url: "/images/local_products_service.png",
				},
				{
					id: "srv-mangrove",
					name: "Exploration de la Mangrove",
					description:
						"Naviguez à travers la mangrove préservée avec notre approche éco-responsable. Découvrez la biodiversité unique de cet écosystème.",
					duration_minutes: 150,
					buffer_time_minutes: 30,
					base_price: 40,
					max_participants: 6,
					min_age: 8,
					max_age: null,
					is_family_friendly: true,
					is_active: true,
					image_url: "/images/mangrove_exploration_service.png",
				},
				{
					id: "srv-sunset-family",
					name: "Aventure Famille au Coucher du Soleil",
					description:
						"Moment magique en famille au coucher du soleil. Observation de la faune et photos souvenirs dans un cadre privilégié.",
					duration_minutes: 120,
					buffer_time_minutes: 20,
					base_price: 50,
					max_participants: 10,
					min_age: 0,
					max_age: null,
					is_family_friendly: true,
					is_active: true,
					image_url: "/images/sunset_family_adventure_service.png",
				},
			])
			.select();

		if (servicesError) throw servicesError;

		// 4. Insert Service Equipment Requirements
		console.log("🔗 Linking services to equipment...");
		const { error: requirementsError } = await supabaseAdmin.from("service_equipment_requirements").insert([
			{ service_id: "srv-waterbikes", equipment_id: "eq-waterbikes", capacity_per_participant: 1 },
			{ service_id: "srv-cultural-tour", equipment_id: "eq-boat-small", capacity_per_participant: 1 },
			{ service_id: "srv-pelican-encounter", equipment_id: "eq-boat-small", capacity_per_participant: 1 },
			{ service_id: "srv-pelican-encounter", equipment_id: "eq-observation-gear", capacity_per_participant: 1 },
			{ service_id: "srv-local-products", equipment_id: "eq-observation-gear", capacity_per_participant: 1 },
			{ service_id: "srv-mangrove", equipment_id: "eq-kayaks", capacity_per_participant: 1 },
			{ service_id: "srv-sunset-family", equipment_id: "eq-boat-small", capacity_per_participant: 1 },
		]);

		if (requirementsError) throw requirementsError;

		// 5. Insert Pricing Tiers
		console.log("💰 Inserting pricing tiers...");
		const pricingTiers = [];
		for (const service of services!) {
			// Child pricing (0-12 years)
			pricingTiers.push({
				service_id: service.id,
				tier_name: "Enfant",
				min_age: 0,
				max_age: 12,
				price: Math.round(service.base_price * 0.7), // 30% discount for children
				is_active: true,
			});

			// Adult pricing (13+ years)
			pricingTiers.push({
				service_id: service.id,
				tier_name: "Adulte",
				min_age: 13,
				max_age: null,
				price: service.base_price,
				is_active: true,
			});
		}

		const { error: pricingError } = await supabaseAdmin.from("pricing_tiers").insert(pricingTiers);

		if (pricingError) throw pricingError;

		// 6. Insert Employees
		console.log("👥 Inserting employees...");
		const { data: employees, error: employeesError } = await supabaseAdmin
			.from("employees")
			.insert([
				{
					id: "emp-sophie",
					first_name: "Sophie",
					last_name: "Laroche",
					email: "<EMAIL>",
					phone: "+33 6 40 24 44 25",
					role: "guide",
					skills: ["navigation", "histoire_locale", "observation_faune"],
					languages: ["français", "anglais"],
					is_active: true,
				},
				{
					id: "emp-marc",
					first_name: "Marc",
					last_name: "Dubois",
					email: "<EMAIL>",
					phone: "+33 6 45 67 89 12",
					role: "guide",
					skills: ["kayak", "écologie", "photographie"],
					languages: ["français", "créole"],
					is_active: true,
				},
			])
			.select();

		if (employeesError) throw employeesError;

		// 7. Insert Sample Customers
		console.log("👤 Inserting sample customers...");
		const { data: customers, error: customersError } = await supabaseAdmin
			.from("customers")
			.insert([
				{
					id: "cust-marie",
					email: "<EMAIL>",
					first_name: "Marie",
					last_name: "Dupont",
					phone: "+33 6 12 34 56 78",
					emergency_contact_name: "Pierre Dupont",
					emergency_contact_phone: "+33 6 87 65 43 21",
				},
				{
					id: "cust-jean",
					email: "<EMAIL>",
					first_name: "Jean",
					last_name: "Martin",
					phone: "+33 6 98 76 54 32",
					emergency_contact_name: "Claire Martin",
					emergency_contact_phone: "+33 6 11 22 33 44",
				},
			])
			.select();

		if (customersError) throw customersError;

		console.log("✅ Database seeding completed successfully!");
		return { success: true };
	} catch (error) {
		console.error("❌ Error seeding database:", error);
		throw error;
	}
}

// Run the seeding if this file is executed directly
if (require.main === module) {
	seedDatabase()
		.then(() => {
			console.log("🎉 Seeding completed!");
			process.exit(0);
		})
		.catch((error) => {
			console.error("💥 Seeding failed:", error);
			process.exit(1);
		});
}

export { seedDatabase };
