"use client";

import AvailabilityCalendar from "@/components/AvailabilityCalendar";
import BookingForm from "@/components/BookingForm";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { ServiceWithPricing, TimeSlotWithAvailability } from "@/lib/types";
import { AlertCircle, ArrowLeft, CheckCircle, Loader2 } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

type BookingStep = "service" | "datetime" | "details" | "confirmation";

export default function BookingPage() {
	const searchParams = useSearchParams();
	const router = useRouter();
	const serviceId = searchParams.get("service");

	const [currentStep, setCurrentStep] = useState<BookingStep>("service");
	const [service, setService] = useState<ServiceWithPricing | null>(null);
	const [selectedDate, setSelectedDate] = useState<Date>();
	const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlotWithAvailability>();
	const [participantCount, setParticipantCount] = useState(1);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		if (serviceId) {
			fetchService();
		} else {
			setError("Aucun service sélectionné");
			setLoading(false);
		}
	}, [serviceId]);

	const fetchService = async () => {
		try {
			const response = await fetch(`/api/services/${serviceId}`);
			if (!response.ok) {
				throw new Error("Service non trouvé");
			}
			const data = await response.json();
			setService(data.service);
			setCurrentStep("datetime");
		} catch (err) {
			setError(err instanceof Error ? err.message : "Erreur lors du chargement du service");
		} finally {
			setLoading(false);
		}
	};

	const handleTimeSlotSelect = (timeSlot: TimeSlotWithAvailability, date: Date) => {
		setSelectedTimeSlot(timeSlot);
		setSelectedDate(date);
		setCurrentStep("details");
	};

	const handleBookingComplete = () => {
		setCurrentStep("confirmation");
	};

	const handleBackToServices = () => {
		router.push("/services");
	};

	const handleBackStep = () => {
		switch (currentStep) {
			case "details":
				setCurrentStep("datetime");
				break;
			case "datetime":
				setCurrentStep("service");
				break;
			case "confirmation":
				setCurrentStep("details");
				break;
		}
	};

	const getStepTitle = () => {
		switch (currentStep) {
			case "service":
				return "Sélection du service";
			case "datetime":
				return "Date et heure";
			case "details":
				return "Informations de réservation";
			case "confirmation":
				return "Confirmation";
			default:
				return "Réservation";
		}
	};

	if (loading) {
		return (
			<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 flex items-center justify-center">
				<div className="flex items-center gap-3">
					<Loader2 className="w-8 h-8 animate-spin text-emerald-600" />
					<span className="text-emerald-600 font-medium">Chargement...</span>
				</div>
			</div>
		);
	}

	if (error) {
		return (
			<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50 flex items-center justify-center">
				<Card className="max-w-md mx-auto">
					<CardContent className="p-6 text-center">
						<AlertCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
						<h2 className="text-xl font-semibold mb-2">Erreur</h2>
						<p className="text-muted-foreground mb-4">{error}</p>
						<Button onClick={handleBackToServices}>
							<ArrowLeft className="w-4 h-4 mr-2" />
							Retour aux services
						</Button>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className="min-h-screen bg-gradient-to-b from-sky-50 to-emerald-50">
			<div className="container mx-auto px-4 py-8">
				{/* Header */}
				<div className="mb-8">
					<div className="flex items-center gap-4 mb-4">
						<Button
							variant="outline"
							size="sm"
							onClick={currentStep === "service" ? handleBackToServices : handleBackStep}
						>
							<ArrowLeft className="w-4 h-4 mr-2" />
							Retour
						</Button>
						<div>
							<h1 className="text-3xl font-bold text-gray-900">{getStepTitle()}</h1>
							{service && (
								<p className="text-muted-foreground">
									{service.name} - {service.duration} - {service.capacity}
								</p>
							)}
						</div>
					</div>

					{/* Progress Steps */}
					<div className="flex items-center gap-2">
						{["datetime", "details", "confirmation"].map((step, index) => {
							const isActive = currentStep === step;
							const isCompleted = ["datetime", "details", "confirmation"].indexOf(currentStep) > index;

							return (
								<div key={step} className="flex items-center">
									<div
										className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
											isCompleted
												? "bg-emerald-600 text-white"
												: isActive
												? "bg-emerald-100 text-emerald-600 border-2 border-emerald-600"
												: "bg-gray-200 text-gray-500"
										}`}
									>
										{isCompleted ? <CheckCircle className="w-4 h-4" /> : index + 1}
									</div>
									{index < 2 && (
										<div
											className={`w-12 h-1 mx-2 ${
												isCompleted ? "bg-emerald-600" : "bg-gray-200"
											}`}
										/>
									)}
								</div>
							);
						})}
					</div>
				</div>

				{/* Service Info Card */}
				{service && (
					<Card className="mb-8">
						<CardContent className="p-6">
							<div className="flex items-start gap-4">
								{service.image && (
									<img
										src={service.image}
										alt={service.name}
										className="w-24 h-24 object-cover rounded-lg"
									/>
								)}
								<div className="flex-1">
									<h3 className="text-xl font-semibold mb-2">{service.name}</h3>
									<p className="text-muted-foreground mb-3">{service.description}</p>
									<div className="flex flex-wrap gap-2">
										<Badge variant="secondary">{service.duration}</Badge>
										<Badge variant="secondary">{service.capacity}</Badge>
										<Badge variant="secondary">{service.ageLimit}</Badge>
										<Badge variant="outline">À partir de {service.price}€</Badge>
									</div>
								</div>
							</div>
						</CardContent>
					</Card>
				)}

				{/* Step Content */}
				{currentStep === "datetime" && service && (
					<AvailabilityCalendar
						serviceId={service.id}
						participantCount={participantCount}
						onTimeSlotSelect={handleTimeSlotSelect}
						selectedDate={selectedDate}
						selectedTimeSlot={selectedTimeSlot}
					/>
				)}

				{currentStep === "details" && service && selectedTimeSlot && selectedDate && (
					<BookingForm
						service={service}
						timeSlot={selectedTimeSlot}
						date={selectedDate}
						onBookingComplete={handleBookingComplete}
					/>
				)}

				{currentStep === "confirmation" && (
					<Card>
						<CardContent className="p-8 text-center">
							<CheckCircle className="w-16 h-16 text-emerald-600 mx-auto mb-4" />
							<h2 className="text-2xl font-bold mb-2">Réservation confirmée !</h2>
							<p className="text-muted-foreground mb-6">
								Votre réservation a été créée avec succès. Vous recevrez un email de confirmation sous
								peu.
							</p>
							<div className="flex gap-4 justify-center">
								<Button onClick={handleBackToServices}>Retour aux services</Button>
								<Button variant="outline" onClick={() => router.push("/account/bookings")}>
									Mes réservations
								</Button>
							</div>
						</CardContent>
					</Card>
				)}
			</div>
		</div>
	);
}
