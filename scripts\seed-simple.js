require('dotenv').config({ path: '.env.local' });
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', supabaseUrl ? 'Set' : 'Missing');
  console.log('SUPABASE_SERVICE_ROLE_KEY:', supabaseServiceKey ? 'Set' : 'Missing');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: { autoRefreshToken: false, persistSession: false }
});

async function seedDatabase() {
  console.log('🌱 Starting database seeding...');
  
  try {
    // Clear existing data
    console.log('🧹 Clearing existing data...');
    await supabase.from('time_slots').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('pricing_tiers').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('service_equipment_requirements').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('equipment').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('services').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('employees').delete().neq('id', '00000000-0000-0000-0000-000000000000');
    await supabase.from('customers').delete().neq('id', '00000000-0000-0000-0000-000000000000');

    // Insert Equipment
    console.log('🛠️ Inserting equipment...');
    const { data: equipment, error: equipmentError } = await supabase
      .from('equipment')
      .insert([
        { id: 'eq-waterbikes', name: 'WaterBikes', description: 'Vélos aquatiques écologiques', total_capacity: 8, status: 'operational' },
        { id: 'eq-kayaks', name: 'Kayaks', description: 'Kayaks pour exploration mangrove', total_capacity: 6, status: 'operational' },
        { id: 'eq-boat-small', name: 'Petit Bateau', description: 'Bateau pour visites guidées', total_capacity: 12, status: 'operational' },
        { id: 'eq-observation-gear', name: 'Matériel d\'observation', description: 'Jumelles et équipement d\'observation', total_capacity: 15, status: 'operational' }
      ])
      .select();

    if (equipmentError) throw equipmentError;

    // Insert Services
    console.log('🏖️ Inserting services...');
    const { data: services, error: servicesError } = await supabase
      .from('services')
      .insert([
        {
          id: 'srv-waterbikes',
          name: 'Excursion en WaterBikes',
          description: 'Explorez le littoral de manière ludique et éco-responsable avec nos vélos aquatiques.',
          duration_minutes: 120,
          buffer_time_minutes: 30,
          base_price: 25,
          max_participants: 8,
          min_age: 12,
          is_family_friendly: true,
          is_active: true,
          image_url: '/images/waterbikes_service.png'
        },
        {
          id: 'srv-cultural-tour',
          name: 'Visite Guidée Culturelle',
          description: 'Plongez au cœur de l\'histoire guadeloupéenne avec notre guide passionné.',
          duration_minutes: 180,
          buffer_time_minutes: 15,
          base_price: 30,
          max_participants: 12,
          min_age: 0,
          is_family_friendly: true,
          is_active: true,
          image_url: '/images/cultural_tour_service.png'
        },
        {
          id: 'srv-pelican-encounter',
          name: 'Rencontre avec les Pélicans',
          description: 'Aventure unique au cœur d\'un îlet sauvage pour observer les pélicans.',
          duration_minutes: 180,
          buffer_time_minutes: 30,
          base_price: 45,
          max_participants: 8,
          min_age: 0,
          is_family_friendly: true,
          is_active: true,
          image_url: '/images/pelican_encounter_service.png'
        },
        {
          id: 'srv-local-products',
          name: 'Dégustation de Produits Locaux',
          description: 'Expérience gustative avec des saveurs authentiques de la Guadeloupe.',
          duration_minutes: 90,
          buffer_time_minutes: 15,
          base_price: 35,
          max_participants: 15,
          min_age: 0,
          is_family_friendly: true,
          is_active: true,
          image_url: '/images/local_products_service.png'
        },
        {
          id: 'srv-mangrove',
          name: 'Exploration de la Mangrove',
          description: 'Naviguez à travers la mangrove préservée avec notre approche éco-responsable.',
          duration_minutes: 150,
          buffer_time_minutes: 30,
          base_price: 40,
          max_participants: 6,
          min_age: 8,
          is_family_friendly: true,
          is_active: true,
          image_url: '/images/mangrove_exploration_service.png'
        },
        {
          id: 'srv-sunset-family',
          name: 'Aventure Famille au Coucher du Soleil',
          description: 'Moment magique en famille au coucher du soleil.',
          duration_minutes: 120,
          buffer_time_minutes: 20,
          base_price: 50,
          max_participants: 10,
          min_age: 0,
          is_family_friendly: true,
          is_active: true,
          image_url: '/images/sunset_family_adventure_service.png'
        }
      ])
      .select();

    if (servicesError) throw servicesError;

    // Insert Service Equipment Requirements
    console.log('🔗 Linking services to equipment...');
    const { error: requirementsError } = await supabase
      .from('service_equipment_requirements')
      .insert([
        { service_id: 'srv-waterbikes', equipment_id: 'eq-waterbikes', capacity_per_participant: 1 },
        { service_id: 'srv-cultural-tour', equipment_id: 'eq-boat-small', capacity_per_participant: 1 },
        { service_id: 'srv-pelican-encounter', equipment_id: 'eq-boat-small', capacity_per_participant: 1 },
        { service_id: 'srv-pelican-encounter', equipment_id: 'eq-observation-gear', capacity_per_participant: 1 },
        { service_id: 'srv-local-products', equipment_id: 'eq-observation-gear', capacity_per_participant: 1 },
        { service_id: 'srv-mangrove', equipment_id: 'eq-kayaks', capacity_per_participant: 1 },
        { service_id: 'srv-sunset-family', equipment_id: 'eq-boat-small', capacity_per_participant: 1 }
      ]);

    if (requirementsError) throw requirementsError;

    // Insert Pricing Tiers
    console.log('💰 Inserting pricing tiers...');
    const pricingTiers = [];
    for (const service of services) {
      // Child pricing (0-12 years)
      pricingTiers.push({
        service_id: service.id,
        tier_name: 'Enfant',
        min_age: 0,
        max_age: 12,
        price: Math.round(service.base_price * 0.7), // 30% discount for children
        is_active: true
      });
      
      // Adult pricing (13+ years)
      pricingTiers.push({
        service_id: service.id,
        tier_name: 'Adulte',
        min_age: 13,
        max_age: null,
        price: service.base_price,
        is_active: true
      });
    }

    const { error: pricingError } = await supabase
      .from('pricing_tiers')
      .insert(pricingTiers);

    if (pricingError) throw pricingError;

    console.log('✅ Database seeding completed successfully!');
    console.log(`📊 Created ${services.length} services with pricing tiers`);
    console.log(`🛠️ Created ${equipment.length} equipment types`);
    console.log('🎉 Ready to test the website!');
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    process.exit(1);
  }
}

seedDatabase();
