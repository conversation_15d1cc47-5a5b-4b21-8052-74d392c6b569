-- =====================================================
-- Admin System Enhancement Migration
-- Production-Ready Scheduling Platform
-- =====================================================

-- Employee availability and working hours
CREATE TABLE employee_availability (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
  day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0=Sunday, 1=Monday, etc.
  start_time TIME NOT NULL,
  end_time TIME NOT NULL,
  is_available BOOLEAN DEFAULT true,
  effective_from DATE,
  effective_until DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure end_time is after start_time
  CONSTRAINT valid_time_range CHECK (end_time > start_time),
  -- Ensure effective dates are logical
  CONSTRAINT valid_effective_dates CHECK (effective_until IS NULL OR effective_until >= effective_from)
);

-- Employee time off and exceptions
CREATE TABLE employee_time_off (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  start_time TIME,
  end_time TIME,
  reason TEXT,
  type VARCHAR(20) DEFAULT 'time_off' CHECK (type IN ('vacation', 'sick', 'time_off', 'training', 'personal')),
  status VARCHAR(20) DEFAULT 'approved' CHECK (status IN ('pending', 'approved', 'denied', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure end_date is after or equal to start_date
  CONSTRAINT valid_date_range CHECK (end_date >= start_date),
  -- If times are specified, ensure end_time is after start_time
  CONSTRAINT valid_time_off_range CHECK (
    (start_time IS NULL AND end_time IS NULL) OR 
    (start_time IS NOT NULL AND end_time IS NOT NULL AND end_time > start_time)
  )
);

-- Service scheduling rules and constraints
CREATE TABLE service_scheduling_rules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  day_of_week INTEGER CHECK (day_of_week IS NULL OR (day_of_week >= 0 AND day_of_week <= 6)), -- NULL for all days
  min_advance_booking_hours INTEGER DEFAULT 24 CHECK (min_advance_booking_hours >= 0),
  max_advance_booking_days INTEGER DEFAULT 365 CHECK (max_advance_booking_days > 0),
  min_time_between_bookings_minutes INTEGER DEFAULT 0 CHECK (min_time_between_bookings_minutes >= 0),
  operating_start_time TIME,
  operating_end_time TIME,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure operating hours are logical
  CONSTRAINT valid_operating_hours CHECK (
    (operating_start_time IS NULL AND operating_end_time IS NULL) OR
    (operating_start_time IS NOT NULL AND operating_end_time IS NOT NULL AND operating_end_time > operating_start_time)
  )
);

-- Service blackout dates
CREATE TABLE service_blackout_dates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  reason TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Ensure end_date is after or equal to start_date
  CONSTRAINT valid_blackout_range CHECK (end_date >= start_date)
);

-- Employee service qualifications
CREATE TABLE employee_service_qualifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employee_id UUID REFERENCES employees(id) ON DELETE CASCADE,
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  qualification_level VARCHAR(20) DEFAULT 'qualified' CHECK (qualification_level IN ('trainee', 'qualified', 'expert', 'instructor')),
  certified_date DATE,
  expiry_date DATE,
  notes TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Unique constraint to prevent duplicate qualifications
  UNIQUE(employee_id, service_id),
  -- Ensure expiry date is after certification date
  CONSTRAINT valid_certification_dates CHECK (expiry_date IS NULL OR expiry_date > certified_date)
);

-- Schedule templates for recurring patterns
CREATE TABLE schedule_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  service_id UUID REFERENCES services(id) ON DELETE CASCADE,
  template_data JSONB NOT NULL, -- Stores recurring pattern rules
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Admin audit log
CREATE TABLE admin_audit_log (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  admin_user_id UUID, -- References profiles(id) but not enforced for flexibility
  action VARCHAR(100) NOT NULL,
  table_name VARCHAR(100),
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  session_id VARCHAR(255),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- Enhance existing tables
-- =====================================================

-- Add scheduling fields to services table
ALTER TABLE services 
ADD COLUMN scheduling_rules JSONB,
ADD COLUMN default_employee_id UUID REFERENCES employees(id),
ADD COLUMN requires_qualification BOOLEAN DEFAULT false,
ADD COLUMN concurrent_capacity INTEGER DEFAULT 1 CHECK (concurrent_capacity > 0),
ADD COLUMN auto_assign_employees BOOLEAN DEFAULT true;

-- Add availability fields to employees table  
ALTER TABLE employees 
ADD COLUMN default_hourly_rate DECIMAL(10,2) CHECK (default_hourly_rate >= 0),
ADD COLUMN is_available_for_scheduling BOOLEAN DEFAULT true,
ADD COLUMN max_concurrent_services INTEGER DEFAULT 1 CHECK (max_concurrent_services > 0),
ADD COLUMN scheduling_priority INTEGER DEFAULT 50 CHECK (scheduling_priority >= 0 AND scheduling_priority <= 100);

-- Enhance time_slots table
ALTER TABLE time_slots 
ADD COLUMN template_id UUID REFERENCES schedule_templates(id),
ADD COLUMN auto_generated BOOLEAN DEFAULT false,
ADD COLUMN override_reason TEXT,
ADD COLUMN capacity_override INTEGER CHECK (capacity_override > 0),
ADD COLUMN requires_confirmation BOOLEAN DEFAULT false;

-- =====================================================
-- Create indexes for performance
-- =====================================================

-- Employee availability indexes
CREATE INDEX idx_employee_availability_employee_day ON employee_availability(employee_id, day_of_week);
CREATE INDEX idx_employee_availability_effective_dates ON employee_availability(effective_from, effective_until);

-- Employee time off indexes
CREATE INDEX idx_employee_time_off_employee_dates ON employee_time_off(employee_id, start_date, end_date);
CREATE INDEX idx_employee_time_off_status ON employee_time_off(status) WHERE status = 'approved';

-- Service scheduling rules indexes
CREATE INDEX idx_service_scheduling_rules_service ON service_scheduling_rules(service_id);
CREATE INDEX idx_service_scheduling_rules_day ON service_scheduling_rules(day_of_week);

-- Service blackout dates indexes
CREATE INDEX idx_service_blackout_dates_service ON service_blackout_dates(service_id);
CREATE INDEX idx_service_blackout_dates_range ON service_blackout_dates(start_date, end_date);

-- Employee qualifications indexes
CREATE INDEX idx_employee_qualifications_employee ON employee_service_qualifications(employee_id);
CREATE INDEX idx_employee_qualifications_service ON employee_service_qualifications(service_id);
CREATE INDEX idx_employee_qualifications_active ON employee_service_qualifications(is_active) WHERE is_active = true;

-- Schedule templates indexes
CREATE INDEX idx_schedule_templates_service ON schedule_templates(service_id);
CREATE INDEX idx_schedule_templates_active ON schedule_templates(is_active) WHERE is_active = true;

-- Admin audit log indexes
CREATE INDEX idx_admin_audit_log_user ON admin_audit_log(admin_user_id);
CREATE INDEX idx_admin_audit_log_table_record ON admin_audit_log(table_name, record_id);
CREATE INDEX idx_admin_audit_log_created_at ON admin_audit_log(created_at);

-- Time slots enhanced indexes
CREATE INDEX idx_time_slots_template ON time_slots(template_id);
CREATE INDEX idx_time_slots_auto_generated ON time_slots(auto_generated) WHERE auto_generated = true;
